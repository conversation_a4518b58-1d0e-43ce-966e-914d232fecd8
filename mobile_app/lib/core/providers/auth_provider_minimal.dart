import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// User model for authentication
class AppUser {
  final String uid;
  final String email;
  final String? displayName;
  final String? photoURL;
  final bool isEmailVerified;
  final DateTime? lastSignInTime;

  const AppUser({
    required this.uid,
    required this.email,
    this.displayName,
    this.photoURL,
    required this.isEmailVerified,
    this.lastSignInTime,
  });

  AppUser copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    bool? isEmailVerified,
    DateTime? lastSignInTime,
  }) {
    return AppUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      lastSignInTime: lastSignInTime ?? this.lastSignInTime,
    );
  }
}

/// Authentication state
class AuthState {
  final AppUser? user;
  final bool isLoading;
  final String? error;
  final bool isFirstTime;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isFirstTime = true,
  });

  AuthState copyWith({
    AppUser? user,
    bool? isLoading,
    String? error,
    bool? isFirstTime,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isFirstTime: isFirstTime ?? this.isFirstTime,
    );
  }

  bool get isAuthenticated => user != null;
  bool get isGuest => user == null && !isLoading;
}

/// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>(
  (ref) => AuthNotifier(),
);

/// Current user provider
final currentUserProvider = Provider<AppUser?>((ref) {
  return ref.watch(authProvider).user;
});

/// Authentication status provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

/// Minimal Auth notifier class for iOS build testing
class AuthNotifier extends StateNotifier<AuthState> {
  static const String _firstTimeKey = 'is_first_time';

  AuthNotifier() : super(const AuthState()) {
    _init();
  }

  /// Initialize auth state
  Future<void> _init() async {
    state = state.copyWith(isLoading: true);

    try {
      // Check if first time user
      final prefs = await SharedPreferences.getInstance();
      final isFirstTime = prefs.getBool(_firstTimeKey) ?? true;

      // For now, just set initial state
      state = state.copyWith(
        isLoading: false,
        isFirstTime: isFirstTime,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Mock sign in with email and password
  Future<bool> signInWithEmailPassword(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Mock authentication - replace with REST API calls
      await Future.delayed(const Duration(seconds: 1));
      
      final user = AppUser(
        uid: 'mock_uid_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        displayName: 'Test User',
        isEmailVerified: true,
        lastSignInTime: DateTime.now(),
      );

      state = state.copyWith(
        user: user,
        isLoading: false,
        error: null,
      );
      
      await _setNotFirstTime();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Authentication failed',
      );
      return false;
    }
  }

  /// Mock sign up with email and password
  Future<bool> signUpWithEmailPassword(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Mock registration - replace with REST API calls
      await Future.delayed(const Duration(seconds: 1));
      
      final user = AppUser(
        uid: 'mock_uid_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        displayName: 'New User',
        isEmailVerified: false,
        lastSignInTime: DateTime.now(),
      );

      state = state.copyWith(
        user: user,
        isLoading: false,
        error: null,
      );
      
      await _setNotFirstTime();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed',
      );
      return false;
    }
  }

  /// Mock send OTP for phone authentication
  Future<bool> sendOTP(String phoneNumber) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Mock OTP sending - replace with REST API calls
      await Future.delayed(const Duration(seconds: 1));
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to send OTP',
      );
      return false;
    }
  }

  /// Mock verify OTP and sign in
  Future<bool> verifyOTP(String otp) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Mock OTP verification - replace with REST API calls
      await Future.delayed(const Duration(seconds: 1));
      
      final user = AppUser(
        uid: 'mock_uid_${DateTime.now().millisecondsSinceEpoch}',
        email: '<EMAIL>',
        displayName: 'Phone User',
        isEmailVerified: true,
        lastSignInTime: DateTime.now(),
      );

      state = state.copyWith(
        user: user,
        isLoading: false,
        error: null,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to verify OTP',
      );
      return false;
    }
  }

  /// Mock register with phone number
  Future<bool> registerWithPhone({
    required String phoneNumber,
    required String name,
    required String email,
  }) async {
    return await sendOTP(phoneNumber);
  }

  /// Mock reset password
  Future<bool> resetPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await Future.delayed(const Duration(seconds: 1));
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to send reset email',
      );
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    state = state.copyWith(isLoading: true);
    
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      state = state.copyWith(
        user: null,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      debugPrint('Error signing out: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Set not first time
  Future<void> _setNotFirstTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_firstTimeKey, false);
      state = state.copyWith(isFirstTime: false);
    } catch (e) {
      debugPrint('Error setting first time flag: $e');
    }
  }
}

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';

class FirebaseAuthRestService {
  static const String _baseUrl = 'https://identitytoolkit.googleapis.com/v1';
  static const String _apiKey = 'YOUR_FIREBASE_API_KEY'; // Replace with your actual API key
  
  // Send OTP to phone number
  static Future<Map<String, dynamic>> sendOTP(String phoneNumber) async {
    final url = Uri.parse('$_baseUrl/accounts:sendVerificationCode?key=$_apiKey');
    
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'phoneNumber': phoneNumber,
        'recaptchaToken': 'dummy_token', // For testing, implement reCAPTCHA in production
      }),
    );
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to send OTP: ${response.body}');
    }
  }
  
  // Verify OTP and sign in
  static Future<Map<String, dynamic>> verifyOTP(String sessionInfo, String code) async {
    final url = Uri.parse('$_baseUrl/accounts:signInWithPhoneNumber?key=$_apiKey');
    
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'sessionInfo': sessionInfo,
        'code': code,
      }),
    );
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to verify OTP: ${response.body}');
    }
  }
  
  // Get user info
  static Future<Map<String, dynamic>> getUserInfo(String idToken) async {
    final url = Uri.parse('$_baseUrl/accounts:lookup?key=$_apiKey');
    
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'idToken': idToken,
      }),
    );
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get user info: ${response.body}');
    }
  }
  
  // Refresh token
  static Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    final url = Uri.parse('https://securetoken.googleapis.com/v1/token?key=$_apiKey');
    
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'grant_type': 'refresh_token',
        'refresh_token': refreshToken,
      }),
    );
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to refresh token: ${response.body}');
    }
  }
  
  // Sign out (client-side only)
  static Future<void> signOut() async {
    // Clear local storage/preferences
    // This is handled in the auth provider
  }
}
